#!/usr/bin/env node

/**
 * DermaCare Webhook Testing Utility
 *
 * Comprehensive testing tool for all webhook endpoints in the DermaCare bi-directional sync system.
 * Tests CC webhooks, AP webhooks, error scenarios, and edge cases.
 *
 * **Key Features:**
 * - Creates real database records for testing with actual IDs
 * - Tests all webhook endpoints with realistic data
 * - Validates webhook processing with proper database references
 * - Supports multiple environments and test suites
 * - Comprehensive error scenario testing
 *
 * **Database Integration:**
 * - Creates test patients, appointments, invoices, and payments
 * - Uses real IDs from database records in webhook payloads
 * - Ensures webhook processing can find and validate referenced records
 * - Cleans up test data after completion
 *
 * Usage:
 *   node webhook-tester.js [environment] [test-suite] [--cleanup]
 *
 * Examples:
 *   node webhook-tester.js local all
 *   node webhook-tester.js staging cc-webhooks
 *   node webhook-tester.js production ap-webhooks --cleanup
 */

// Import required modules
const { Pool } = require('@neondatabase/serverless');
const crypto = require('crypto');

// Database configuration
const DATABASE_CONFIG = {
  local: {
    connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/dermacare_local'
  },
  staging: {
    connectionString: process.env.STAGING_DATABASE_URL || process.env.DATABASE_URL
  },
  production: {
    connectionString: process.env.PRODUCTION_DATABASE_URL || process.env.DATABASE_URL
  },
  ngrok: {
    connectionString: process.env.DATABASE_URL
  }
};

// Global variables for test data cleanup
let createdTestData = {
  patients: [],
  appointments: [],
  invoices: [],
  payments: []
};

// Configuration for different environments
const ENVIRONMENTS = {
  local: {
    baseUrl: 'http://localhost:8787',
    name: 'Local Development'
  },
  staging: {
    baseUrl: 'https://staging-dermacare-sync.example.com',
    name: 'Staging Environment'
  },
  production: {
    baseUrl: 'https://dermacare-sync.example.com',
    name: 'Production Environment'
  },
  ngrok: {
    baseUrl: 'https://proven-moose-hopefully.ngrok-free.app',
    name: 'Ngrok Tunnel'
  }
};

// Database helper functions
async function getDbConnection(environment) {
  const config = DATABASE_CONFIG[environment];
  if (!config || !config.connectionString) {
    throw new Error(`No database configuration found for environment: ${environment}`);
  }

  return new Pool({ connectionString: config.connectionString });
}

async function createTestPatient(db, patientData = {}) {
  const id = crypto.randomUUID();
  const ccId = Math.floor(Math.random() * 1000000) + 100000; // Random 6-digit number

  const patient = {
    id,
    cc_id: ccId,
    email: patientData.email || `test-${Date.now()}@example.com`,
    phone: patientData.phone || `+1${Math.floor(Math.random() * **********) + **********}`,
    cc_updated_at: new Date().toISOString(),
    cc_data: JSON.stringify({
      id: ccId,
      firstName: patientData.firstName || 'Test',
      lastName: patientData.lastName || 'Patient',
      email: patientData.email || `test-${Date.now()}@example.com`,
      phoneMobile: patientData.phone || `+1${Math.floor(Math.random() * **********) + **********}`,
      dob: patientData.dob || '1990-01-01',
      gender: patientData.gender || 'male',
      appointments: [],
      invoices: [],
      customFields: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const query = `
    INSERT INTO patients (id, cc_id, email, phone, cc_updated_at, cc_data, created_at, updated_at)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    RETURNING *
  `;

  const values = [
    patient.id,
    patient.cc_id,
    patient.email,
    patient.phone,
    patient.cc_updated_at,
    patient.cc_data,
    patient.created_at,
    patient.updated_at
  ];

  const result = await db.query(query, values);
  const createdPatient = result.rows[0];

  // Track for cleanup
  createdTestData.patients.push(createdPatient.id);

  return createdPatient;
}

async function createTestAppointment(db, patientId, ccPatientId, appointmentData = {}) {
  const id = crypto.randomUUID();
  const ccId = Math.floor(Math.random() * 1000000) + 100000; // Random 6-digit number

  const appointment = {
    id,
    cc_id: ccId,
    patient_id: patientId,
    cc_updated_at: new Date().toISOString(),
    cc_data: JSON.stringify({
      id: ccId,
      title: appointmentData.title || 'Test Appointment',
      startsAt: appointmentData.startsAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      endsAt: appointmentData.endsAt || new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
      patients: [ccPatientId],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const query = `
    INSERT INTO appointments (id, cc_id, patient_id, cc_updated_at, cc_data, created_at, updated_at)
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *
  `;

  const values = [
    appointment.id,
    appointment.cc_id,
    appointment.patient_id,
    appointment.cc_updated_at,
    appointment.cc_data,
    appointment.created_at,
    appointment.updated_at
  ];

  const result = await db.query(query, values);
  const createdAppointment = result.rows[0];

  // Track for cleanup
  createdTestData.appointments.push(createdAppointment.id);

  return createdAppointment;
}

async function cleanupTestData(db) {
  console.log('🧹 Cleaning up test data...');

  try {
    // Delete appointments first (due to foreign key constraints)
    if (createdTestData.appointments.length > 0) {
      const appointmentIds = createdTestData.appointments.map(id => `'${id}'`).join(',');
      await db.query(`DELETE FROM appointments WHERE id IN (${appointmentIds})`);
      console.log(`   Deleted ${createdTestData.appointments.length} test appointments`);
    }

    // Delete patients
    if (createdTestData.patients.length > 0) {
      const patientIds = createdTestData.patients.map(id => `'${id}'`).join(',');
      await db.query(`DELETE FROM patients WHERE id IN (${patientIds})`);
      console.log(`   Deleted ${createdTestData.patients.length} test patients`);
    }

    // Reset tracking arrays
    createdTestData = {
      patients: [],
      appointments: [],
      invoices: [],
      payments: []
    };

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  }
}

// Test data generation functions (using real database records)
async function generateTestData(db) {
  console.log('📊 Creating test database records...');

  // Create test patient
  const testPatient = await createTestPatient(db, {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+**********",
    dob: "1990-01-01",
    gender: "male"
  });

  const ccPatientData = JSON.parse(testPatient.cc_data);

  // Create test appointment
  const testAppointment = await createTestAppointment(db, testPatient.id, testPatient.cc_id, {
    title: "Test Appointment",
    startsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    endsAt: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString()
  });

  const ccAppointmentData = JSON.parse(testAppointment.cc_data);

  console.log(`   Created test patient with CC ID: ${testPatient.cc_id}`);
  console.log(`   Created test appointment with CC ID: ${testAppointment.cc_id}`);

  return {
    ccPatient: {
      event: "EntityWasCreated",
      model: "Patient",
      id: testPatient.cc_id,
      payload: ccPatientData
    },

    ccAppointment: {
      event: "AppointmentWasCreated",
      model: "Appointment",
      id: testAppointment.cc_id,
      payload: ccAppointmentData
    },

    ccInvoice: {
      event: "EntityWasCreated",
      model: "Invoice",
      id: Math.floor(Math.random() * 1000000) + 100000,
      payload: {
        id: Math.floor(Math.random() * 1000000) + 100000,
        patient: testPatient.cc_id,
        amount: 150.00,
        status: "paid",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        pdfUrl: "https://example.com/invoice.pdf"
      }
    },

    ccPayment: {
      event: "EntityWasCreated",
      model: "Payment",
      id: Math.floor(Math.random() * 1000000) + 100000,
      payload: {
        id: Math.floor(Math.random() * 1000000) + 100000,
        patient: testPatient.cc_id,
        amount: 150.00,
        status: "completed",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        pdfUrl: "https://example.com/payment.pdf"
      }
    },

    apContact: {
      event: "contact.created",
      data: {
        contact: {
          id: `ap_contact_${Date.now()}`,
          firstName: "Jane",
          lastName: "Smith",
          email: `jane.smith.${Date.now()}@example.com`,
          phone: `+1${Math.floor(Math.random() * **********) + **********}`,
          dateAdded: new Date().toISOString(),
          dateUpdated: new Date().toISOString(),
          tags: ["test"],
          customFields: []
        }
      }
    },

    apAppointment: {
      event: "appointment.created",
      data: {
        calendar: {
          appointmentId: `ap_appointment_${Date.now()}`,
          startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          endTime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
          title: "AP Test Appointment",
          created_by_meta: {
            source: "user"
          },
          last_updated_by_meta: {
            source: "user"
          }
        },
        contact_id: `ap_contact_${Date.now()}`
      }
    }
  };
}

// Test suites generator (uses real test data)
function generateTestSuites(testData) {
  return {
    'cc-webhooks': [
      { name: 'CC Patient Created', endpoint: '/webhook', data: testData.ccPatient },
      { name: 'CC Patient Updated', endpoint: '/webhook', data: { ...testData.ccPatient, event: "EntityWasUpdated" } },
      { name: 'CC Appointment Created', endpoint: '/webhook', data: testData.ccAppointment },
      { name: 'CC Appointment Updated', endpoint: '/webhook', data: { ...testData.ccAppointment, event: "EntityWasUpdated" } },
      { name: 'CC Invoice Created', endpoint: '/webhook', data: testData.ccInvoice },
      { name: 'CC Payment Created', endpoint: '/webhook', data: testData.ccPayment }
    ],

    'ap-webhooks': [
      { name: 'AP Contact Created', endpoint: '/ap/contact', data: testData.apContact },
      { name: 'AP Contact Updated', endpoint: '/ap/contact', data: { ...testData.apContact, event: "contact.updated" } },
      { name: 'AP Appointment Created', endpoint: '/ap/appointment', data: testData.apAppointment },
      { name: 'AP Appointment Updated', endpoint: '/ap/appointment', data: { ...testData.apAppointment, event: "appointment.updated" } }
    ],

    'error-scenarios': [
      { name: 'Invalid JSON', endpoint: '/webhook', data: 'invalid-json', expectError: true },
      { name: 'Missing Event Type', endpoint: '/webhook', data: { model: "Patient", id: 123 }, expectError: true },
      { name: 'Unknown Model', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "UnknownModel", id: 123 }, expectError: true },
      { name: 'Missing Payload', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "Patient", id: 123 }, expectError: true }
    ],

    'edge-cases': [
      { name: 'Third Party AP Appointment', endpoint: '/ap/appointment', data: {
        ...testData.apAppointment,
        data: {
          ...testData.apAppointment.data,
          calendar: {
            ...testData.apAppointment.data.calendar,
            created_by_meta: { source: "third_party" }
          }
        }
      }},
      { name: 'Empty Email and Phone', endpoint: '/webhook', data: {
        ...testData.ccPatient,
        payload: { ...testData.ccPatient.payload, email: "", phoneMobile: "" }
      }},
      { name: 'Duplicate Processing', endpoint: '/webhook', data: testData.ccPatient }
    ],

    'health-check': [
      { name: 'Health Check', endpoint: '/health', method: 'GET' }
    ]
  };
}

// Utility functions
async function makeRequest(baseUrl, endpoint, data = null, method = 'POST') {
  const url = `${baseUrl}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'DermaCare-Webhook-Tester/1.0'
    }
  };
  
  if (data && method !== 'GET') {
    options.body = typeof data === 'string' ? data : JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: parsedData,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    return {
      error: error.message,
      status: 0
    };
  }
}

function formatResult(test, result) {
  const status = result.status;
  const isSuccess = result.expectError ? (status >= 400) : (status >= 200 && status < 300);
  const icon = isSuccess ? '✅' : '❌';
  
  console.log(`${icon} ${test.name}`);
  console.log(`   Status: ${status} ${result.statusText || ''}`);
  
  if (result.error) {
    console.log(`   Error: ${result.error}`);
  } else if (result.data) {
    if (typeof result.data === 'object') {
      console.log(`   Response: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`);
    } else {
      console.log(`   Response: ${result.data.substring(0, 200)}...`);
    }
  }
  
  console.log('');
  return isSuccess;
}

async function runTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;
    
    const success = formatResult(test, result);
    if (success) passed++;
    
    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`📊 Results: ${passed}/${total} tests passed`);
  return { passed, total };
}

async function main() {
  const args = process.argv.slice(2);
  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  
  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }
  
  const environment = ENVIRONMENTS[envName];
  
  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);
  
  let totalPassed = 0;
  let totalTests = 0;
  
  const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;
  
  for (const suiteName of suitesToRun) {
    if (!TEST_SUITES[suiteName]) {
      console.error(`❌ Unknown test suite: ${suiteName}`);
      console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all`);
      continue;
    }
    
    const result = await runTestSuite(environment, suiteName, TEST_SUITES[suiteName]);
    totalPassed += result.passed;
    totalTests += result.total;
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the results above.');
    process.exit(1);
  }
}

// Note: Stress tests removed for simplicity in this implementation
// They can be added back later if needed with proper test data generation

// Performance monitoring
function measurePerformance(testName, startTime) {
  const endTime = Date.now();
  const duration = endTime - startTime;

  if (duration > 25000) {
    console.log(`⚠️  ${testName} took ${duration}ms (exceeds 25s limit)`);
  } else if (duration > 10000) {
    console.log(`🟡 ${testName} took ${duration}ms (slow but acceptable)`);
  } else {
    console.log(`🟢 ${testName} took ${duration}ms (good performance)`);
  }

  return duration;
}

// Enhanced test runner with performance monitoring
async function runEnhancedTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));

  let passed = 0;
  let total = tests.length;
  const performanceData = [];

  for (const test of tests) {
    const startTime = Date.now();
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;

    const duration = measurePerformance(test.name, startTime);
    performanceData.push({ name: test.name, duration });

    const success = formatResult(test, result);
    if (success) passed++;

    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Performance summary
  const avgDuration = performanceData.reduce((sum, p) => sum + p.duration, 0) / performanceData.length;
  const maxDuration = Math.max(...performanceData.map(p => p.duration));

  console.log(`📊 Results: ${passed}/${total} tests passed`);
  console.log(`⏱️  Performance: Avg ${avgDuration.toFixed(0)}ms, Max ${maxDuration}ms`);

  return { passed, total, performanceData };
}

// CLI help
function showHelp() {
  console.log(`
🚀 DermaCare Webhook Testing Utility

Usage:
  node webhook-tester.js [environment] [test-suite] [options]

Environments:
  local      - Local development server (http://localhost:8787)
  ngrok      - Ngrok tunnel (https://proven-moose-hopefully.ngrok-free.app)
  staging    - Staging environment
  production - Production environment

Test Suites:
  all           - Run all test suites
  cc-webhooks   - CC (CliniCore) webhook tests
  ap-webhooks   - AP (AutoPatient) webhook tests
  error-scenarios - Error handling tests
  edge-cases    - Edge case scenarios
  health-check  - Health check endpoint
  stress        - Stress and performance tests

Options:
  --help, -h    - Show this help message
  --verbose, -v - Verbose output
  --performance - Include performance monitoring

Examples:
  node webhook-tester.js local all
  node webhook-tester.js ngrok cc-webhooks
  node webhook-tester.js staging ap-webhooks --performance
  node webhook-tester.js production health-check
`);
}

// Enhanced main function with database integration
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  const verbose = args.includes('--verbose') || args.includes('-v');
  const performanceMode = args.includes('--performance');
  const shouldCleanup = args.includes('--cleanup');

  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }

  const environment = ENVIRONMENTS[envName];

  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);

  // Initialize database connection
  let db = null;
  let testData = null;
  let TEST_SUITES = null;

  try {
    // Connect to database and create test data
    db = await getDbConnection(envName);
    console.log('🔌 Connected to database');

    // Generate test data with real database records
    testData = await generateTestData(db);
    console.log('✅ Test data generation completed');

    // Generate test suites with real data
    TEST_SUITES = generateTestSuites(testData);

    let totalPassed = 0;
    let totalTests = 0;
    let allPerformanceData = [];

    const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;

    // Run test suites
    for (const suiteName of suitesToRun.filter(s => s !== 'stress')) {
      if (!TEST_SUITES[suiteName]) {
        console.error(`❌ Unknown test suite: ${suiteName}`);
        console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all, stress`);
        continue;
      }

      const testRunner = performanceMode ? runEnhancedTestSuite : runTestSuite;
      const result = await testRunner(environment, suiteName, TEST_SUITES[suiteName]);

      totalPassed += result.passed;
      totalTests += result.total;

      if (result.performanceData) {
        allPerformanceData.push(...result.performanceData);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);

    if (performanceMode && allPerformanceData.length > 0) {
      const avgDuration = allPerformanceData.reduce((sum, p) => sum + p.duration, 0) / allPerformanceData.length;
      const slowTests = allPerformanceData.filter(p => p.duration > 10000);

      console.log(`⏱️  Overall Performance: Avg ${avgDuration.toFixed(0)}ms`);
      if (slowTests.length > 0) {
        console.log(`🐌 Slow tests (>10s): ${slowTests.map(t => t.name).join(', ')}`);
      }
    }

    if (totalPassed === totalTests) {
      console.log('🎉 All tests passed!');
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.');
    }

  } catch (error) {
    console.error('💥 Error during testing:', error.message);
    if (verbose) {
      console.error(error.stack);
    }
  } finally {
    // Cleanup test data if requested or if there was an error
    if (db && (shouldCleanup || totalPassed !== totalTests)) {
      await cleanupTestData(db);
    }

    // Close database connection
    if (db) {
      await db.end();
      console.log('🔌 Database connection closed');
    }
  }

  // Exit with appropriate code
  process.exit(totalPassed === totalTests ? 0 : 1);
}

// Handle command line execution
if (require.main === module) {
  main().catch(console.error);
}
