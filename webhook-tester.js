#!/usr/bin/env node

/**
 * DermaCare Webhook Testing Utility
 * 
 * Comprehensive testing tool for all webhook endpoints in the DermaCare bi-directional sync system.
 * Tests CC webhooks, AP webhooks, error scenarios, and edge cases.
 * 
 * Usage:
 *   node webhook-tester.js [environment] [test-suite]
 * 
 * Examples:
 *   node webhook-tester.js local all
 *   node webhook-tester.js staging cc-webhooks
 *   node webhook-tester.js production ap-webhooks
 */

// Configuration for different environments
const ENVIRONMENTS = {
  local: {
    baseUrl: 'http://localhost:8787',
    name: 'Local Development'
  },
  staging: {
    baseUrl: 'https://staging-dermacare-sync.example.com',
    name: 'Staging Environment'
  },
  production: {
    baseUrl: 'https://dermacare-sync.example.com',
    name: 'Production Environment'
  },
  ngrok: {
    baseUrl: 'https://proven-moose-hopefully.ngrok-free.app',
    name: 'Ngrok Tunnel'
  }
};

// Test data templates
const TEST_DATA = {
  ccPatient: {
    event: "EntityWasCreated",
    model: "Patient",
    id: 12345,
    payload: {
      id: 12345,
      firstName: "<PERSON>",
      lastName: "Doe",
      email: "<EMAIL>",
      phoneMobile: "+**********",
      dob: "1990-01-01",
      gender: "male",
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      appointments: [],
      invoices: [],
      customFields: []
    }
  },
  
  ccAppointment: {
    event: "AppointmentWasCreated",
    model: "Appointment",
    id: 67890,
    payload: {
      id: 67890,
      title: "Test Appointment",
      startsAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      endsAt: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
      patients: [12345],
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString()
    }
  },
  
  ccInvoice: {
    event: "EntityWasCreated",
    model: "Invoice",
    id: 11111,
    payload: {
      id: 11111,
      patient: 12345,
      amount: 150.00,
      status: "paid",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      pdfUrl: "https://example.com/invoice.pdf"
    }
  },
  
  ccPayment: {
    event: "EntityWasCreated",
    model: "Payment",
    id: 22222,
    payload: {
      id: 22222,
      patient: 12345,
      amount: 150.00,
      status: "completed",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      pdfUrl: "https://example.com/payment.pdf"
    }
  },
  
  apContact: {
    event: "contact.created",
    data: {
      contact: {
        id: "ap_contact_123",
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+**********",
        dateAdded: new Date().toISOString(),
        dateUpdated: new Date().toISOString(),
        tags: ["test"],
        customFields: []
      }
    }
  },
  
  apAppointment: {
    event: "appointment.created",
    data: {
      calendar: {
        appointmentId: "ap_appointment_456",
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        endTime: new Date(Date.now() + 25 * 60 * 60 * 1000).toISOString(),
        title: "AP Test Appointment",
        created_by_meta: {
          source: "user"
        },
        last_updated_by_meta: {
          source: "user"
        }
      },
      contact_id: "ap_contact_123"
    }
  }
};

// Test suites
const TEST_SUITES = {
  'cc-webhooks': [
    { name: 'CC Patient Created', endpoint: '/webhook', data: TEST_DATA.ccPatient },
    { name: 'CC Patient Updated', endpoint: '/webhook', data: { ...TEST_DATA.ccPatient, event: "EntityWasUpdated" } },
    { name: 'CC Appointment Created', endpoint: '/webhook', data: TEST_DATA.ccAppointment },
    { name: 'CC Appointment Updated', endpoint: '/webhook', data: { ...TEST_DATA.ccAppointment, event: "EntityWasUpdated" } },
    { name: 'CC Invoice Created', endpoint: '/webhook', data: TEST_DATA.ccInvoice },
    { name: 'CC Payment Created', endpoint: '/webhook', data: TEST_DATA.ccPayment }
  ],
  
  'ap-webhooks': [
    { name: 'AP Contact Created', endpoint: '/ap/contact', data: TEST_DATA.apContact },
    { name: 'AP Contact Updated', endpoint: '/ap/contact', data: { ...TEST_DATA.apContact, event: "contact.updated" } },
    { name: 'AP Appointment Created', endpoint: '/ap/appointment', data: TEST_DATA.apAppointment },
    { name: 'AP Appointment Updated', endpoint: '/ap/appointment', data: { ...TEST_DATA.apAppointment, event: "appointment.updated" } }
  ],
  
  'error-scenarios': [
    { name: 'Invalid JSON', endpoint: '/webhook', data: 'invalid-json', expectError: true },
    { name: 'Missing Event Type', endpoint: '/webhook', data: { model: "Patient", id: 123 }, expectError: true },
    { name: 'Unknown Model', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "UnknownModel", id: 123 }, expectError: true },
    { name: 'Missing Payload', endpoint: '/webhook', data: { event: "EntityWasCreated", model: "Patient", id: 123 }, expectError: true }
  ],
  
  'edge-cases': [
    { name: 'Third Party AP Appointment', endpoint: '/ap/appointment', data: { 
      ...TEST_DATA.apAppointment, 
      data: { 
        ...TEST_DATA.apAppointment.data, 
        calendar: { 
          ...TEST_DATA.apAppointment.data.calendar, 
          created_by_meta: { source: "third_party" } 
        } 
      } 
    }},
    { name: 'Empty Email and Phone', endpoint: '/webhook', data: {
      ...TEST_DATA.ccPatient,
      payload: { ...TEST_DATA.ccPatient.payload, email: "", phoneMobile: "" }
    }},
    { name: 'Duplicate Processing', endpoint: '/webhook', data: TEST_DATA.ccPatient }
  ],
  
  'health-check': [
    { name: 'Health Check', endpoint: '/health', method: 'GET' }
  ]
};

// Utility functions
async function makeRequest(baseUrl, endpoint, data = null, method = 'POST') {
  const url = `${baseUrl}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'DermaCare-Webhook-Tester/1.0'
    }
  };
  
  if (data && method !== 'GET') {
    options.body = typeof data === 'string' ? data : JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const responseData = await response.text();
    
    let parsedData;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: parsedData,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    return {
      error: error.message,
      status: 0
    };
  }
}

function formatResult(test, result) {
  const status = result.status;
  const isSuccess = result.expectError ? (status >= 400) : (status >= 200 && status < 300);
  const icon = isSuccess ? '✅' : '❌';
  
  console.log(`${icon} ${test.name}`);
  console.log(`   Status: ${status} ${result.statusText || ''}`);
  
  if (result.error) {
    console.log(`   Error: ${result.error}`);
  } else if (result.data) {
    if (typeof result.data === 'object') {
      console.log(`   Response: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`);
    } else {
      console.log(`   Response: ${result.data.substring(0, 200)}...`);
    }
  }
  
  console.log('');
  return isSuccess;
}

async function runTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;
    
    const success = formatResult(test, result);
    if (success) passed++;
    
    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`📊 Results: ${passed}/${total} tests passed`);
  return { passed, total };
}

async function main() {
  const args = process.argv.slice(2);
  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  
  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }
  
  const environment = ENVIRONMENTS[envName];
  
  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);
  
  let totalPassed = 0;
  let totalTests = 0;
  
  const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;
  
  for (const suiteName of suitesToRun) {
    if (!TEST_SUITES[suiteName]) {
      console.error(`❌ Unknown test suite: ${suiteName}`);
      console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all`);
      continue;
    }
    
    const result = await runTestSuite(environment, suiteName, TEST_SUITES[suiteName]);
    totalPassed += result.passed;
    totalTests += result.total;
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the results above.');
    process.exit(1);
  }
}

// Additional test scenarios for comprehensive coverage
const STRESS_TESTS = {
  'concurrent-requests': async (environment) => {
    console.log('\n🔥 Running concurrent request stress test...');
    const promises = [];
    const testData = TEST_DATA.ccPatient;

    // Send 10 concurrent requests
    for (let i = 0; i < 10; i++) {
      const data = { ...testData, id: testData.id + i };
      promises.push(makeRequest(environment.baseUrl, '/webhook', data));
    }

    const results = await Promise.all(promises);
    const successful = results.filter(r => r.status >= 200 && r.status < 300).length;

    console.log(`📊 Concurrent test: ${successful}/10 requests successful`);
    return successful === 10;
  },

  'large-payload': async (environment) => {
    console.log('\n📦 Testing large payload handling...');
    const largePayload = {
      ...TEST_DATA.ccPatient,
      payload: {
        ...TEST_DATA.ccPatient.payload,
        customFields: Array(100).fill(0).map((_, i) => ({
          id: i,
          name: `CustomField${i}`,
          value: `Value${i}`.repeat(100)
        }))
      }
    };

    const result = await makeRequest(environment.baseUrl, '/webhook', largePayload);
    const success = result.status >= 200 && result.status < 300;

    console.log(`📊 Large payload test: ${success ? 'PASSED' : 'FAILED'} (${result.status})`);
    return success;
  }
};

// Performance monitoring
function measurePerformance(testName, startTime) {
  const endTime = Date.now();
  const duration = endTime - startTime;

  if (duration > 25000) {
    console.log(`⚠️  ${testName} took ${duration}ms (exceeds 25s limit)`);
  } else if (duration > 10000) {
    console.log(`🟡 ${testName} took ${duration}ms (slow but acceptable)`);
  } else {
    console.log(`🟢 ${testName} took ${duration}ms (good performance)`);
  }

  return duration;
}

// Enhanced test runner with performance monitoring
async function runEnhancedTestSuite(environment, suiteName, tests) {
  console.log(`\n🧪 Running ${suiteName} tests on ${environment.name}`);
  console.log(`📍 Base URL: ${environment.baseUrl}`);
  console.log('─'.repeat(60));

  let passed = 0;
  let total = tests.length;
  const performanceData = [];

  for (const test of tests) {
    const startTime = Date.now();
    const method = test.method || 'POST';
    const result = await makeRequest(environment.baseUrl, test.endpoint, test.data, method);
    result.expectError = test.expectError;

    const duration = measurePerformance(test.name, startTime);
    performanceData.push({ name: test.name, duration });

    const success = formatResult(test, result);
    if (success) passed++;

    // Add delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // Performance summary
  const avgDuration = performanceData.reduce((sum, p) => sum + p.duration, 0) / performanceData.length;
  const maxDuration = Math.max(...performanceData.map(p => p.duration));

  console.log(`📊 Results: ${passed}/${total} tests passed`);
  console.log(`⏱️  Performance: Avg ${avgDuration.toFixed(0)}ms, Max ${maxDuration}ms`);

  return { passed, total, performanceData };
}

// CLI help
function showHelp() {
  console.log(`
🚀 DermaCare Webhook Testing Utility

Usage:
  node webhook-tester.js [environment] [test-suite] [options]

Environments:
  local      - Local development server (http://localhost:8787)
  ngrok      - Ngrok tunnel (https://proven-moose-hopefully.ngrok-free.app)
  staging    - Staging environment
  production - Production environment

Test Suites:
  all           - Run all test suites
  cc-webhooks   - CC (CliniCore) webhook tests
  ap-webhooks   - AP (AutoPatient) webhook tests
  error-scenarios - Error handling tests
  edge-cases    - Edge case scenarios
  health-check  - Health check endpoint
  stress        - Stress and performance tests

Options:
  --help, -h    - Show this help message
  --verbose, -v - Verbose output
  --performance - Include performance monitoring

Examples:
  node webhook-tester.js local all
  node webhook-tester.js ngrok cc-webhooks
  node webhook-tester.js staging ap-webhooks --performance
  node webhook-tester.js production health-check
`);
}

// Enhanced main function
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }

  const envName = args[0] || 'local';
  const suiteNames = args[1] ? [args[1]] : ['all'];
  const verbose = args.includes('--verbose') || args.includes('-v');
  const performanceMode = args.includes('--performance');

  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.error(`Available environments: ${Object.keys(ENVIRONMENTS).join(', ')}`);
    process.exit(1);
  }

  const environment = ENVIRONMENTS[envName];

  console.log('🚀 DermaCare Webhook Testing Utility');
  console.log(`🌍 Environment: ${environment.name}`);
  console.log(`🔗 Base URL: ${environment.baseUrl}`);

  let totalPassed = 0;
  let totalTests = 0;
  let allPerformanceData = [];

  const suitesToRun = suiteNames.includes('all') ? Object.keys(TEST_SUITES) : suiteNames;

  // Add stress tests if requested
  if (suiteNames.includes('stress') || suiteNames.includes('all')) {
    console.log('\n🔥 Running stress tests...');
    for (const [testName, testFn] of Object.entries(STRESS_TESTS)) {
      const success = await testFn(environment);
      totalPassed += success ? 1 : 0;
      totalTests += 1;
    }
  }

  for (const suiteName of suitesToRun.filter(s => s !== 'stress')) {
    if (!TEST_SUITES[suiteName]) {
      console.error(`❌ Unknown test suite: ${suiteName}`);
      console.error(`Available suites: ${Object.keys(TEST_SUITES).join(', ')}, all, stress`);
      continue;
    }

    const testRunner = performanceMode ? runEnhancedTestSuite : runTestSuite;
    const result = await testRunner(environment, suiteName, TEST_SUITES[suiteName]);

    totalPassed += result.passed;
    totalTests += result.total;

    if (result.performanceData) {
      allPerformanceData.push(...result.performanceData);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log(`🎯 Overall Results: ${totalPassed}/${totalTests} tests passed`);

  if (performanceMode && allPerformanceData.length > 0) {
    const avgDuration = allPerformanceData.reduce((sum, p) => sum + p.duration, 0) / allPerformanceData.length;
    const slowTests = allPerformanceData.filter(p => p.duration > 10000);

    console.log(`⏱️  Overall Performance: Avg ${avgDuration.toFixed(0)}ms`);
    if (slowTests.length > 0) {
      console.log(`🐌 Slow tests (>10s): ${slowTests.map(t => t.name).join(', ')}`);
    }
  }

  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the results above.');
    process.exit(1);
  }
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { TEST_SUITES, ENVIRONMENTS, makeRequest, runTestSuite, runEnhancedTestSuite, STRESS_TESTS };
