import { Pool } from "@neondatabase/serverless";
import getConfig from "@utils/configs";
import { drizzle } from "drizzle-orm/neon-serverless";
import dbSchema from "./schema";

/**
 * Drizzle ORM client instance, connected to Neon Postgres.
 *
 * @see https://orm.drizzle.team/docs/overview
 */
export const getDb = () => {
	return drizzle(
		new Pool({ connectionString: getConfig("databaseUrl") as string }),
		{ schema: dbSchema },
	);
};

export { dbSchema };
