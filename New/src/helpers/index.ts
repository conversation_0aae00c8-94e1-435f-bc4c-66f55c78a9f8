/**
 * Helper functions module exports
 * Provides centralized access to all helper utilities
 */

// Export custom field utilities
export {
	clearCustomFieldCaches,
	extractCCCustomFieldValues as extractCCPatientCustomFieldValues,
	getAPCustomFieldNameToIdMap,
	getCCCustomFieldIdToConfigMap,
	getCustomFieldCacheStats,
	getOrCreateAPCustomFieldId,
	prepareAPCustomFieldUpdates,
	refreshCustomFieldCaches,
	updateAPContactCustomFields,
} from "./customFields";
// Export data transformation utilities
export {
	calculateServiceAppointmentCounts,
	calculateServiceSpending,
	extractCCCustomFieldValues,
	formatDateToISO,
	formatPhoneNumber,
	isValidEmail,
	isValidPhone,
	reduceCustomFieldValue,
	removeHtmlTags,
	safeJsonParse,
	safeJsonStringify,
	transformAPContactToCCPatient,
	transformCCPatientToAPContact,
} from "./dataTransform";

// Re-export for convenience
export const helpers = {
	dataTransform: () => import("./dataTransform"),
	customFields: () => import("./customFields"),
};
