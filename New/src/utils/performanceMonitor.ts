/**
 * Performance monitoring utilities for DermaCare sync service
 *
 * This module provides comprehensive performance monitoring capabilities to ensure
 * all webhook processing completes within the 25-second Cloudflare Workers timeout.
 * It tracks execution times, identifies bottlenecks, and provides metrics for
 * optimization and monitoring.
 *
 * **Key Features:**
 * - Request-level performance tracking
 * - Database query performance monitoring
 * - API call timing and success rates
 * - Memory usage tracking
 * - Bottleneck identification
 * - Performance alerts and warnings
 *
 * **Performance Targets:**
 * - Total webhook processing: < 25 seconds
 * - Database queries: < 2 seconds each
 * - API calls: < 10 seconds each
 * - Memory usage: < 128MB
 *
 * @example
 * ```typescript
 * // Track overall request performance
 * const requestTimer = startRequestTimer("webhook_processing");
 *
 * // Track database operations
 * const dbTimer = startTimer("database_query");
 * await db.select().from(patients);
 * const dbDuration = endTimer(dbTimer);
 *
 * // Track API calls
 * const apiTimer = startTimer("cc_api_call");
 * const patient = await ccClient.patient.get(123);
 * const apiDuration = endTimer(apiTimer);
 *
 * // Complete request tracking
 * const totalDuration = endRequestTimer(requestTimer);
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { getPerformanceConfig } from "./configs";

/**
 * Performance timer interface for tracking operation durations
 */
interface PerformanceTimer {
	id: string;
	operation: string;
	startTime: number;
	startMemory?: number;
}

/**
 * Performance metrics for completed operations
 */
interface PerformanceMetrics {
	operation: string;
	duration: number;
	memoryUsed?: number;
	success: boolean;
	timestamp: number;
}

/**
 * Request-level performance tracking
 */
interface RequestPerformance {
	requestId: string;
	startTime: number;
	operations: PerformanceMetrics[];
	totalDuration?: number;
	peakMemory?: number;
}

/**
 * Active timers for ongoing operations
 */
const activeTimers = new Map<string, PerformanceTimer>();

/**
 * Request performance tracking
 */
const requestMetrics = new Map<string, RequestPerformance>();

/**
 * Get performance thresholds from centralized configuration
 * Optimized for 25-second webhook completion requirement
 */
const getPerformanceThresholds = () => ({
	TOTAL_REQUEST: getPerformanceConfig('totalRequestTimeout'),
	DATABASE_QUERY: getPerformanceConfig('databaseQueryTimeout'),
	API_CALL: getPerformanceConfig('apiCallTimeout'),
	MEMORY_LIMIT: getPerformanceConfig('memoryLimit'),
	WARNING_THRESHOLD: getPerformanceConfig('warningThreshold'),
	CRITICAL_THRESHOLD: getPerformanceConfig('criticalThreshold'),
	BATCH_OPERATION: getPerformanceConfig('batchOperationTimeout'),
	CACHE_OPERATION: getPerformanceConfig('cacheOperationTimeout'),
});

/**
 * Performance optimization flags
 */
const OPTIMIZATION_FLAGS = {
	ENABLE_QUERY_CACHING: true,
	ENABLE_API_CACHING: true,
	ENABLE_BATCH_PROCESSING: true,
	ENABLE_PARALLEL_PROCESSING: true,
	ENABLE_MEMORY_OPTIMIZATION: true,
	MAX_PARALLEL_OPERATIONS: 3,
} as const;

/**
 * Starts a performance timer for an operation
 *
 * @param operation - Name of the operation being timed
 * @param requestId - Optional request ID for grouping metrics
 * @returns Timer ID for ending the timer
 *
 * @example
 * ```typescript
 * const timerId = startTimer("patient_processing", "req_123");
 * // ... perform operation
 * const duration = endTimer(timerId);
 * ```
 */
export function startTimer(operation: string, requestId?: string): string {
	const timerId = crypto.randomUUID();
	const timer: PerformanceTimer = {
		id: timerId,
		operation,
		startTime: performance.now(),
		startMemory: getMemoryUsage(),
	};

	activeTimers.set(timerId, timer);

	if (requestId) {
		const request = requestMetrics.get(requestId);
		if (request) {
			// Track operation start within request context
			console.log(`[${requestId}] Started ${operation}`);
		}
	}

	return timerId;
}

/**
 * Ends a performance timer and returns metrics
 *
 * @param timerId - Timer ID returned from startTimer
 * @param success - Whether the operation was successful
 * @returns Performance metrics for the operation
 *
 * @example
 * ```typescript
 * const timerId = startTimer("database_query");
 * try {
 *   await db.select().from(patients);
 *   const metrics = endTimer(timerId, true);
 * } catch (error) {
 *   const metrics = endTimer(timerId, false);
 * }
 * ```
 */
export function endTimer(
	timerId: string,
	success: boolean = true,
): PerformanceMetrics | null {
	const timer = activeTimers.get(timerId);
	if (!timer) {
		console.warn(`Timer ${timerId} not found`);
		return null;
	}

	const endTime = performance.now();
	const duration = endTime - timer.startTime;
	const currentMemory = getMemoryUsage();
	const memoryUsed =
		currentMemory && timer.startMemory
			? currentMemory - timer.startMemory
			: undefined;

	const metrics: PerformanceMetrics = {
		operation: timer.operation,
		duration,
		memoryUsed,
		success,
		timestamp: Date.now(),
	};

	// Check for performance warnings
	checkPerformanceThresholds(metrics);

	// Clean up timer
	activeTimers.delete(timerId);

	return metrics;
}

/**
 * Starts request-level performance tracking
 *
 * @param requestId - Unique request identifier
 * @param operation - Type of request (e.g., "webhook_processing")
 * @returns Request performance tracker
 *
 * @example
 * ```typescript
 * const requestTimer = startRequestTimer("req_123", "webhook_processing");
 * // ... process webhook
 * const totalMetrics = endRequestTimer(requestTimer);
 * ```
 */
export function startRequestTimer(
	requestId: string,
	operation: string = "request",
): string {
	const request: RequestPerformance = {
		requestId,
		startTime: performance.now(),
		operations: [],
	};

	requestMetrics.set(requestId, request);

	console.log(`[${requestId}] Started ${operation} tracking`);
	return requestId;
}

/**
 * Ends request-level performance tracking
 *
 * @param requestId - Request ID from startRequestTimer
 * @returns Complete request performance metrics
 *
 * @example
 * ```typescript
 * const requestTimer = startRequestTimer("req_123");
 * // ... process request
 * const metrics = endRequestTimer(requestTimer);
 *
 * if (metrics.totalDuration > 20000) {
 *   console.warn("Request approaching timeout limit");
 * }
 * ```
 */
export function endRequestTimer(requestId: string): RequestPerformance | null {
	const request = requestMetrics.get(requestId);
	if (!request) {
		console.warn(`Request ${requestId} not found`);
		return null;
	}

	const endTime = performance.now();
	request.totalDuration = endTime - request.startTime;
	request.peakMemory = getMemoryUsage();

	// Log performance summary
	logRequestPerformance(request);

	// Check for timeout warnings
	const thresholds = getPerformanceThresholds();
	if (
		request.totalDuration >
		thresholds.TOTAL_REQUEST * thresholds.WARNING_THRESHOLD
	) {
		console.warn(
			`[${requestId}] Request approaching timeout: ${request.totalDuration}ms`,
		);
	}

	// Clean up request tracking
	requestMetrics.delete(requestId);

	return request;
}

/**
 * Adds operation metrics to a request
 *
 * @param requestId - Request ID
 * @param metrics - Operation performance metrics
 */
export function addOperationMetrics(
	requestId: string,
	metrics: PerformanceMetrics,
): void {
	const request = requestMetrics.get(requestId);
	if (request) {
		request.operations.push(metrics);
	}
}

/**
 * Gets current memory usage if available
 *
 * @returns Memory usage in bytes or undefined if not available
 */
function getMemoryUsage(): number | undefined {
	// Note: Memory usage tracking may not be available in all environments
	if (typeof process !== "undefined" && process.memoryUsage) {
		return process.memoryUsage().heapUsed;
	}
	return undefined;
}

/**
 * Checks performance metrics against thresholds and logs warnings
 *
 * @param metrics - Performance metrics to check
 */
function checkPerformanceThresholds(metrics: PerformanceMetrics): void {
	const { operation, duration, memoryUsed } = metrics;

	// Check duration thresholds
	let threshold: number;
	if (operation.includes("database") || operation.includes("db")) {
		threshold = PERFORMANCE_THRESHOLDS.DATABASE_QUERY;
	} else if (operation.includes("api") || operation.includes("client")) {
		threshold = PERFORMANCE_THRESHOLDS.API_CALL;
	} else {
		return; // No specific threshold for this operation
	}

	if (duration > threshold) {
		console.warn(
			`⚠️ Slow ${operation}: ${duration}ms (threshold: ${threshold}ms)`,
		);
	} else if (duration > threshold * PERFORMANCE_THRESHOLDS.WARNING_THRESHOLD) {
		console.log(`🟡 ${operation} approaching threshold: ${duration}ms`);
	}

	// Check memory usage
	if (
		memoryUsed &&
		memoryUsed >
			PERFORMANCE_THRESHOLDS.MEMORY_LIMIT *
				PERFORMANCE_THRESHOLDS.WARNING_THRESHOLD
	) {
		console.warn(
			`⚠️ High memory usage in ${operation}: ${Math.round(memoryUsed / 1024 / 1024)}MB`,
		);
	}
}

/**
 * Logs comprehensive request performance summary
 *
 * @param request - Request performance data
 */
function logRequestPerformance(request: RequestPerformance): void {
	const { requestId, totalDuration, operations, peakMemory } = request;

	console.log(`\n📊 Performance Summary [${requestId}]`);
	console.log(`⏱️ Total Duration: ${totalDuration}ms`);

	if (peakMemory) {
		console.log(`💾 Peak Memory: ${Math.round(peakMemory / 1024 / 1024)}MB`);
	}

	if (operations.length > 0) {
		console.log(`🔧 Operations (${operations.length}):`);
		operations.forEach((op) => {
			const status = op.success ? "✅" : "❌";
			console.log(`  ${status} ${op.operation}: ${op.duration}ms`);
		});

		// Find slowest operation
		const slowest = operations.reduce((prev, current) =>
			prev.duration > current.duration ? prev : current,
		);
		console.log(`🐌 Slowest: ${slowest.operation} (${slowest.duration}ms)`);
	}

	// Performance rating
	if (totalDuration !== undefined) {
		if (totalDuration < PERFORMANCE_THRESHOLDS.TOTAL_REQUEST * 0.5) {
			console.log(`🟢 Performance: Excellent`);
		} else if (
			totalDuration <
			PERFORMANCE_THRESHOLDS.TOTAL_REQUEST *
				PERFORMANCE_THRESHOLDS.WARNING_THRESHOLD
		) {
			console.log(`🟡 Performance: Good`);
		} else {
			console.log(`🔴 Performance: Needs Optimization`);
		}
	}

	console.log(""); // Empty line for readability
}

/**
 * Gets current performance statistics with enhanced metrics
 *
 * @returns Comprehensive performance statistics
 */
export function getPerformanceStats(): {
	activeTimers: number;
	activeRequests: number;
	memoryUsage?: number;
	averageRequestTime?: number;
	slowRequests: number;
	criticalRequests: number;
	optimizationFlags: typeof OPTIMIZATION_FLAGS;
} {
	const completedRequests = Array.from(requestMetrics.values()).filter(
		(r) => r.totalDuration !== undefined,
	);
	const averageRequestTime =
		completedRequests.length > 0
			? completedRequests.reduce((sum, r) => sum + (r.totalDuration || 0), 0) /
				completedRequests.length
			: undefined;

	const slowRequests = completedRequests.filter(
		(r) =>
			(r.totalDuration || 0) >
			PERFORMANCE_THRESHOLDS.TOTAL_REQUEST *
				PERFORMANCE_THRESHOLDS.WARNING_THRESHOLD,
	).length;

	const criticalRequests = completedRequests.filter(
		(r) =>
			(r.totalDuration || 0) >
			PERFORMANCE_THRESHOLDS.TOTAL_REQUEST *
				PERFORMANCE_THRESHOLDS.CRITICAL_THRESHOLD,
	).length;

	return {
		activeTimers: activeTimers.size,
		activeRequests: requestMetrics.size,
		memoryUsage: getMemoryUsage(),
		averageRequestTime,
		slowRequests,
		criticalRequests,
		optimizationFlags: OPTIMIZATION_FLAGS,
	};
}

/**
 * Advanced performance analysis for optimization recommendations
 *
 * @param requestId - Request ID to analyze
 * @returns Performance analysis with optimization recommendations
 */
export function analyzePerformance(requestId: string): {
	analysis: string[];
	recommendations: string[];
	bottlenecks: string[];
	score: number;
} {
	const request = requestMetrics.get(requestId);
	if (!request || !request.totalDuration) {
		return {
			analysis: ["Request not found or incomplete"],
			recommendations: [],
			bottlenecks: [],
			score: 0,
		};
	}

	const analysis: string[] = [];
	const recommendations: string[] = [];
	const bottlenecks: string[] = [];

	// Analyze total duration
	const totalDuration = request.totalDuration;
	const warningThreshold =
		PERFORMANCE_THRESHOLDS.TOTAL_REQUEST *
		PERFORMANCE_THRESHOLDS.WARNING_THRESHOLD;
	const criticalThreshold =
		PERFORMANCE_THRESHOLDS.TOTAL_REQUEST *
		PERFORMANCE_THRESHOLDS.CRITICAL_THRESHOLD;

	if (totalDuration > criticalThreshold) {
		analysis.push(
			`🔴 Critical: Request took ${totalDuration}ms (>${criticalThreshold}ms)`,
		);
		recommendations.push(
			"Implement aggressive caching and parallel processing",
		);
		bottlenecks.push("Overall request duration");
	} else if (totalDuration > warningThreshold) {
		analysis.push(
			`🟡 Warning: Request took ${totalDuration}ms (>${warningThreshold}ms)`,
		);
		recommendations.push("Consider optimizing slow operations");
	} else {
		analysis.push(`🟢 Good: Request completed in ${totalDuration}ms`);
	}

	// Analyze individual operations
	const slowOperations = request.operations.filter((op) => {
		if (op.operation.includes("database"))
			return op.duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY;
		if (op.operation.includes("api"))
			return op.duration > PERFORMANCE_THRESHOLDS.API_CALL;
		return op.duration > PERFORMANCE_THRESHOLDS.BATCH_OPERATION;
	});

	if (slowOperations.length > 0) {
		bottlenecks.push(
			...slowOperations.map((op) => `${op.operation} (${op.duration}ms)`),
		);
		recommendations.push("Optimize slow operations identified in bottlenecks");
	}

	// Calculate performance score (0-100)
	const score = Math.max(
		0,
		Math.min(
			100,
			100 - (totalDuration / PERFORMANCE_THRESHOLDS.TOTAL_REQUEST) * 100,
		),
	);

	return { analysis, recommendations, bottlenecks, score };
}

/**
 * Cleanup old performance data to prevent memory leaks
 * Should be called periodically to maintain performance
 */
export function cleanupPerformanceData(): void {
	const now = Date.now();
	const maxAge = 10 * 60 * 1000; // 10 minutes

	// Clean up old request metrics
	for (const [requestId, request] of requestMetrics.entries()) {
		if (now - request.startTime > maxAge) {
			requestMetrics.delete(requestId);
		}
	}

	// Clean up orphaned timers
	for (const [timerId, timer] of activeTimers.entries()) {
		if (now - timer.startTime > maxAge) {
			console.warn(`Cleaning up orphaned timer: ${timer.operation}`);
			activeTimers.delete(timerId);
		}
	}
}

/**
 * Clears all performance tracking data
 * Should only be used for testing or emergency cleanup
 */
export function clearPerformanceData(): void {
	activeTimers.clear();
	requestMetrics.clear();
	console.log("Performance tracking data cleared");
}
