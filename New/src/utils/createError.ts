import { HTTPException } from "hono/http-exception";
import type { ContentfulStatusCode } from "hono/utils/http-status";

/**
 * Enhanced error creation options
 */
export interface ErrorOptions {
	/** Error message */
	message: string;
	/** HTTP status code */
	status: ContentfulStatusCode;
	/** Error cause (original error) */
	cause?: unknown;
	/** Response object */
	res?: Response;
	/** Additional error context */
	context?: Record<string, unknown>;
	/** Error code for categorization */
	code?: string;
}

/**
 * Common HTTP status codes for convenience
 */
export const HTTP_STATUS = {
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	CONFLICT: 409,
	UNPROCESSABLE_ENTITY: 422,
	TOO_MANY_REQUESTS: 429,
	INTERNAL_SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
} as const;

/**
 * Enhanced error creation utility with better type safety and context support
 *
 * @param messageOrOptions - Error message string or detailed options object
 * @param status - HTTP status code (used only when first param is string)
 * @returns HTTPException instance with enhanced error information
 *
 * @example
 * ```typescript
 * // Simple error
 * throw createError("User not found", 404);
 *
 * // Detailed error with context
 * throw createError({
 *   message: "Failed to sync patient data",
 *   status: 422,
 *   code: "SYNC_ERROR",
 *   context: { patientId: 123, platform: "CC" },
 *   cause: originalError
 * });
 * ```
 */
const createError = (
	messageOrOptions: string | ErrorOptions,
	status: ContentfulStatusCode = 500,
): HTTPException => {
	if (typeof messageOrOptions === "string") {
		return new HTTPException(status, {
			message: messageOrOptions,
			// Add timestamp for better debugging
			cause: {
				timestamp: new Date().toISOString(),
				status,
			},
		});
	}

	const {
		message,
		cause,
		res,
		status: statusCode,
		context,
		code,
	} = messageOrOptions;

	// Enhanced cause object with additional context
	const enhancedCause = {
		timestamp: new Date().toISOString(),
		status: statusCode || status,
		code,
		context,
		originalCause: cause,
	};

	return new HTTPException(statusCode || status, {
		message,
		cause: enhancedCause,
		res,
	});
};

/**
 * Create a validation error (400 Bad Request)
 * @param message - Error message
 * @param context - Additional validation context
 * @returns HTTPException for validation errors
 */
export const createValidationError = (
	message: string,
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.BAD_REQUEST,
		code: "VALIDATION_ERROR",
		context,
	});
};

/**
 * Create an authentication error (401 Unauthorized)
 * @param message - Error message
 * @param context - Additional auth context
 * @returns HTTPException for authentication errors
 */
export const createAuthError = (
	message: string = "Authentication required",
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.UNAUTHORIZED,
		code: "AUTH_ERROR",
		context,
	});
};

/**
 * Create a not found error (404 Not Found)
 * @param resource - Resource that was not found
 * @param context - Additional context
 * @returns HTTPException for not found errors
 */
export const createNotFoundError = (
	resource: string,
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message: `${resource} not found`,
		status: HTTP_STATUS.NOT_FOUND,
		code: "NOT_FOUND",
		context,
	});
};

/**
 * Create a sync error (422 Unprocessable Entity)
 * @param message - Error message
 * @param platform - Platform where sync failed
 * @param context - Additional sync context
 * @returns HTTPException for sync errors
 */
export const createSyncError = (
	message: string,
	platform: "CC" | "AP",
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
		code: "SYNC_ERROR",
		context: {
			platform,
			...context,
		},
	});
};

export default createError;
