// Re-export all utility modules

export {
	// Legacy exports for backward compatibility (deprecated)
	AdvancedCache,
	type CacheConfig,
	type CacheStats,
	CloudflareCache,
	type CloudflareCacheConfig,
	createApiResponseCache,
	createAppointmentCache,
	createCustomFieldCache,
	createPatientCache,
	getCombinedCacheStats,
} from "./advancedCache";
export { default as cleanData } from "./cleanData";
export {
	type AppConfigs,
	default as getConfig,
	getConfigs,
	getCacheTTL,
	getCircuitBreakerConfig,
	getConcurrencyConfig,
	getErrorLoggingConfig,
	getOptimizationConfig,
	getPerformanceConfig,
	getRetryConfig,
	validateConfig,
} from "./configs";
export {
	default as createError,
	createSimpleError,
	getErrorMessage,
	getErrorStatus,
	isHttpError,
} from "./createError";
export {
	AP_CUSTOM_FIELDS,
	AP_INVOICE_CUSTOM_FIELDS,
	AP_PAYMENT_CUSTOM_FIELDS,
	FINANCIAL_CUSTOM_FIELDS,
	PATIENT_INFO_CUSTOM_FIELDS,
	SERVICE_APPOINTMENT_COUNT_PREFIX,
	SERVICE_SPENDING_PREFIX,
	type CustomFieldValue,
	type FinancialMetrics,
	type LatestInvoiceData,
	type LatestPaymentData,
	type ServiceAppointmentCounts,
	type ServiceSpendingAmounts,
	extractServiceNameFromAppointmentField,
	extractServiceNameFromSpendingField,
	getServiceAppointmentCountFieldName,
	getServiceSpendingFieldName,
	isServiceAppointmentCountField,
	isServiceSpendingField,
} from "./customFieldDefinitions";
export { logApiError, logError, logSyncError } from "./errorLogger";
export {
	formatDateToISO,
	formatPhoneNumber,
	hasRequiredProperties,
	isNonEmptyString,
	isPositiveNumber,
	isValidContactData,
	isValidEmail,
	isValidId,
	isValidPatientData,
	isValidPhone,
} from "./validation";
export {
	type ProcessorResponse,
	createErrorResponse,
	createLogMessage,
	createSkippedResponse,
	createSuccessResponse,
	extractErrorMessage,
	handleApiError,
	handleProcessorError,
	validateDatabaseResult,
	validateEmailOrPhone,
	validateRequiredFields,
	withBufferCheck,
} from "./processorHelpers";
export { type LogContext, logger } from "./logger";
export { matchString } from "./matchString";
export {
	analyzePerformance,
	cleanupPerformanceData,
	endRequestTimer,
	endTimer,
	getPerformanceStats,
	startRequestTimer,
	startTimer,
} from "./performanceMonitor";
export {
	type BatchOperation,
	batchDatabaseOperations,
	cachedApiCall,
	createOptimizedProcessor,
	DEFAULT_OPTIMIZATION_CONFIG,
	type OptimizationConfig,
	type OptimizationResult,
	optimizePatientProcessing,
	optimizeWebhookProcessing,
	parallelApiCalls,
} from "./performanceOptimizer";
// export { SlackLogger, slackLogger } from "./slackLogger"; // TODO: Implement slackLogger if needed

/**
 * Enhanced console logger with visual separators and structured output
 * @deprecated Use the `logger` utility instead for better formatting and consistency
 * @param msg - Message to display
 * @param data - Optional data to display as JSON
 */
export const cLog = (msg: string, data: unknown = null): void => {
	const separator = "=".repeat(100);
	const divider = "-".repeat(100);

	console.log(separator);
	console.log(" ");
	console.log(msg);

	if (data !== null && data !== undefined) {
		console.log(" ");
		console.log(divider);
		console.log(" ");

		try {
			// Better JSON formatting with proper error handling
			const jsonString =
				typeof data === "string" ? data : JSON.stringify(data, null, 2);
			console.log(jsonString);
		} catch {
			// Fallback for non-serializable objects
			console.log(String(data));
		}
	}

	console.log(" ");
	console.log(separator);
};

/**
 * Safely reduces an array of custom field values into a comma-separated string
 * @param values - Array of values or single value to process
 * @returns Comma-separated string or original value if not an array
 */
export const reduceCustomFieldValue = (values: unknown): string | unknown => {
	if (!Array.isArray(values)) {
		return values;
	}

	// Filter out null, undefined, and empty string values
	const validValues = values.filter(
		(value) =>
			value !== null && value !== undefined && String(value).trim() !== "",
	);

	if (validValues.length === 0) {
		return "";
	}

	return validValues.map((value) => String(value).trim()).join(", ");
};

/**
 * Removes HTML tags and cleans up whitespace from a string
 * @param str - Input string that may contain HTML
 * @returns Cleaned string with HTML tags removed and normalized whitespace
 */
export const removeHtmlTags = (str: string): string => {
	if (typeof str !== "string") {
		return "";
	}

	return (
		str
			// Remove HTML tags (improved pattern to handle self-closing tags and attributes)
			.replace(/<[^>]*>/g, "")
			// Remove square brackets (commonly used in markup)
			.replace(/\[|\]/g, "")
			// Normalize whitespace (replace multiple spaces/tabs/newlines with single space)
			.replace(/\s+/g, " ")
			// Trim leading and trailing whitespace
			.trim()
	);
};

/**
 * Recursively removes null, undefined, and empty string properties from objects and arrays
 * @deprecated Use cleanData() instead for more configurable data cleaning
 * @param obj - Object or array to clean
 * @returns Cleaned object/array with null/empty properties removed
 */
export const removeNullEmptyProperties = <T>(obj: T): T | undefined => {
	// Use the more advanced cleanData utility for consistency
	return cleanData(obj, {
		removeEmptyStrings: true,
		removeEmptyArrays: true,
		removeEmptyObjects: true,
		trimStrings: false, // Preserve original behavior
	}) as T | undefined;
};
