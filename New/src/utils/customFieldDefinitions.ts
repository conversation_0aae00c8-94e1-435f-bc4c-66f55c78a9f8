/**
 * Custom Field Definitions for DermaCare Data Sync
 * 
 * This module defines all custom field names and configurations used in the
 * bi-directional sync between CliniCore (CC) and AutoPatient (AP) systems.
 * These definitions ensure consistent field naming and provide a centralized
 * location for managing custom field mappings.
 * 
 * **Field Categories:**
 * - Invoice-related custom fields
 * - Payment-related custom fields  
 * - Financial calculation fields (LTV, due amounts, etc.)
 * - Service appointment counting fields
 * - Service spending calculation fields
 * 
 * **Usage:**
 * ```typescript
 * import { AP_CUSTOM_FIELDS, FINANCIAL_CUSTOM_FIELDS } from '@utils/customFieldDefinitions';
 * 
 * // Use predefined field names
 * const fieldName = AP_CUSTOM_FIELDS.INVOICE.LATEST_PDF_URL;
 * const ltvField = FINANCIAL_CUSTOM_FIELDS.LIFETIME_VALUE;
 * ```
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * Invoice-related custom field definitions
 * These fields track the latest invoice information for each patient
 */
export const AP_INVOICE_CUSTOM_FIELDS = {
	/** Latest invoice PDF URL */
	LATEST_PDF_URL: 'Latest Invoice PDF URL',
	
	/** Latest invoice gross amount */
	LATEST_GROSS_AMOUNT: 'Latest Gross Amount',
	
	/** Latest invoice discount amount */
	LATEST_DISCOUNT: 'Latest Discount',
	
	/** Latest invoice total amount */
	LATEST_TOTAL_AMOUNT: 'Latest Total Amount',
	
	/** Latest payment status for the invoice */
	LATEST_PAYMENT_STATUS: 'Latest Payment Status',
	
	/** Latest invoice products/services */
	LATEST_PRODUCTS: 'Latest Products',
	
	/** Latest invoice diagnosis */
	LATEST_DIAGNOSIS: 'Latest Diagnosis',
	
	/** Latest invoice treated by (practitioner) */
	LATEST_TREATED_BY: 'Latest Treated By',
} as const;

/**
 * Payment-related custom field definitions
 * These fields track the latest payment information for each patient
 */
export const AP_PAYMENT_CUSTOM_FIELDS = {
	/** Latest payment status */
	LATEST_STATUS: 'Latest Payment Status',
	
	/** Latest amount paid */
	LATEST_AMOUNT: 'Latest Amount Paid',
	
	/** Latest payment date */
	LATEST_DATE: 'Latest Payment Date',
	
	/** Latest payment PDF URL */
	LATEST_PDF_URL: 'Latest Payment PDF URL',
} as const;

/**
 * Financial calculation custom field definitions
 * These fields store calculated financial metrics for each patient
 */
export const FINANCIAL_CUSTOM_FIELDS = {
	/** Outstanding due amount */
	DUE_AMOUNT: 'Due amount',
	
	/** Available credit amount */
	CREDIT_AMOUNT: 'Credit amount',
	
	/** Total invoiced amount across all invoices */
	TOTAL_INVOICED_AMOUNT: 'Total Invoiced Amount',
	
	/** Calculated lifetime value of the patient */
	LIFETIME_VALUE: 'Life Time Value',
} as const;

/**
 * Service appointment counting field prefix
 * Used to create dynamic fields like "Total appointments booked for [Service Name]"
 */
export const SERVICE_APPOINTMENT_COUNT_PREFIX = 'Total appointments booked for ';

/**
 * Service spending calculation field prefix  
 * Used to create dynamic fields like "Total amount paid for [Service Name]"
 */
export const SERVICE_SPENDING_PREFIX = 'Total amount paid for ';

/**
 * Basic patient information custom fields
 * These fields store fundamental patient data in AP
 */
export const PATIENT_INFO_CUSTOM_FIELDS = {
	/** CC Patient ID for cross-reference */
	PATIENT_ID: 'Patient ID',
	
	/** Total number of appointments */
	TOTAL_APPOINTMENTS: 'Total Appointments',
	
	/** Patient title (Mr., Mrs., Dr., etc.) */
	TITLE: 'Title',
	
	/** Patient title suffix (Jr., Sr., III, etc.) */
	TITLE_SUFFIX: 'Title Suffix',
} as const;

/**
 * All custom field definitions combined for easy access
 */
export const AP_CUSTOM_FIELDS = {
	INVOICE: AP_INVOICE_CUSTOM_FIELDS,
	PAYMENT: AP_PAYMENT_CUSTOM_FIELDS,
	FINANCIAL: FINANCIAL_CUSTOM_FIELDS,
	PATIENT_INFO: PATIENT_INFO_CUSTOM_FIELDS,
} as const;

/**
 * Type definitions for custom field values
 */
export type CustomFieldValue = string | number;

/**
 * Interface for service appointment counts
 * Maps service names to appointment counts
 */
export interface ServiceAppointmentCounts {
	[serviceName: string]: number;
}

/**
 * Interface for service spending amounts
 * Maps service names to total spending amounts
 */
export interface ServiceSpendingAmounts {
	[serviceName: string]: number;
}

/**
 * Interface for financial metrics
 */
export interface FinancialMetrics {
	dueAmount: number;
	creditAmount: number;
	totalInvoicedAmount: number;
	lifetimeValue: number;
}

/**
 * Interface for latest invoice data
 */
export interface LatestInvoiceData {
	pdfUrl?: string;
	grossAmount?: number;
	discount?: number;
	totalAmount?: number;
	paymentStatus?: string;
	products?: string;
	diagnosis?: string;
	treatedBy?: string;
}

/**
 * Interface for latest payment data
 */
export interface LatestPaymentData {
	status?: string;
	amount?: number;
	date?: string;
	pdfUrl?: string;
}

/**
 * Utility function to generate service appointment count field name
 * 
 * @param serviceName - Name of the service
 * @returns Custom field name for service appointment count
 */
export function getServiceAppointmentCountFieldName(serviceName: string): string {
	return `${SERVICE_APPOINTMENT_COUNT_PREFIX}${serviceName}`;
}

/**
 * Utility function to generate service spending field name
 * 
 * @param serviceName - Name of the service
 * @returns Custom field name for service spending amount
 */
export function getServiceSpendingFieldName(serviceName: string): string {
	return `${SERVICE_SPENDING_PREFIX}${serviceName}`;
}

/**
 * Utility function to check if a field name is a service appointment count field
 * 
 * @param fieldName - Custom field name to check
 * @returns True if the field is a service appointment count field
 */
export function isServiceAppointmentCountField(fieldName: string): boolean {
	return fieldName.startsWith(SERVICE_APPOINTMENT_COUNT_PREFIX);
}

/**
 * Utility function to check if a field name is a service spending field
 * 
 * @param fieldName - Custom field name to check
 * @returns True if the field is a service spending field
 */
export function isServiceSpendingField(fieldName: string): boolean {
	return fieldName.startsWith(SERVICE_SPENDING_PREFIX);
}

/**
 * Utility function to extract service name from appointment count field name
 * 
 * @param fieldName - Service appointment count field name
 * @returns Service name or null if not a valid field name
 */
export function extractServiceNameFromAppointmentField(fieldName: string): string | null {
	if (!isServiceAppointmentCountField(fieldName)) {
		return null;
	}
	return fieldName.substring(SERVICE_APPOINTMENT_COUNT_PREFIX.length);
}

/**
 * Utility function to extract service name from spending field name
 * 
 * @param fieldName - Service spending field name
 * @returns Service name or null if not a valid field name
 */
export function extractServiceNameFromSpendingField(fieldName: string): string | null {
	if (!isServiceSpendingField(fieldName)) {
		return null;
	}
	return fieldName.substring(SERVICE_SPENDING_PREFIX.length);
}
