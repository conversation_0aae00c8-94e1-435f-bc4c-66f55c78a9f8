/**
 * Validation utilities for DermaCare data sync
 * 
 * This module provides comprehensive validation functions for data integrity
 * and format validation across the DermaCare sync service. It includes
 * validation for emails, phone numbers, dates, and other common data types.
 * 
 * **Key Features:**
 * - Email format validation with comprehensive regex
 * - Phone number validation with international support
 * - Date validation and format checking
 * - String validation utilities
 * - Type-safe validation functions
 * 
 * **Performance:**
 * - Optimized regex patterns for fast validation
 * - Minimal memory footprint
 * - Efficient string processing
 * 
 * @example
 * ```typescript
 * import { isValidEmail, isValidPhone, formatPhoneNumber } from '@utils/validation';
 * 
 * // Validate email
 * if (isValidEmail("<EMAIL>")) {
 *   console.log("Valid email");
 * }
 * 
 * // Validate and format phone
 * if (isValidPhone("+1234567890")) {
 *   const formatted = formatPhoneNumber("+1234567890");
 *   console.log("Formatted phone:", formatted);
 * }
 * ```
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * Validates email format using comprehensive regex pattern
 * 
 * This function uses a robust email validation regex that covers most
 * common email formats while being performant for high-volume validation.
 * It checks for proper structure, valid characters, and basic format rules.
 *
 * @param email - Email string to validate
 * @returns True if valid email format, false otherwise
 * 
 * @example
 * ```typescript
 * isValidEmail("<EMAIL>");     // true
 * isValidEmail("<EMAIL>");  // true
 * isValidEmail("invalid.email");        // false
 * isValidEmail("");                     // false
 * isValidEmail(null);                   // false
 * ```
 */
export function isValidEmail(email: string): boolean {
	if (!email || typeof email !== "string") {
		return false;
	}

	// Comprehensive email regex pattern
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email.trim());
}

/**
 * Validates phone number format
 * 
 * This function validates phone numbers in various formats including:
 * - International format with country code (+1234567890)
 * - US format with parentheses ((*************)
 * - US format with dashes (************)
 * - Simple numeric format (1234567890)
 * 
 * @param phone - Phone string to validate
 * @returns True if valid phone format, false otherwise
 * 
 * @example
 * ```typescript
 * isValidPhone("+1234567890");      // true
 * isValidPhone("(*************");   // true
 * isValidPhone("************");     // true
 * isValidPhone("1234567890");       // true
 * isValidPhone("123");              // false
 * isValidPhone("");                 // false
 * ```
 */
export function isValidPhone(phone: string): boolean {
	if (!phone || typeof phone !== "string") {
		return false;
	}

	// Remove all non-digit characters to check length
	const digitsOnly = phone.replace(/\D/g, "");
	
	// Valid phone numbers should have 10-15 digits
	return digitsOnly.length >= 10 && digitsOnly.length <= 15;
}

/**
 * Formats phone number to a standard format
 * 
 * This function takes a phone number in various formats and converts it
 * to a standardized format. It handles US and international numbers.
 * 
 * @param phone - Phone string to format
 * @returns Formatted phone string or original if can't format
 * 
 * @example
 * ```typescript
 * formatPhoneNumber("1234567890");      // "(*************"
 * formatPhoneNumber("11234567890");     // "+1 (*************"
 * formatPhoneNumber("+1234567890");     // "+1 (*************"
 * formatPhoneNumber("invalid");         // "invalid"
 * ```
 */
export function formatPhoneNumber(phone: string): string {
	if (!phone || typeof phone !== "string") {
		return "";
	}

	// Remove all non-digit characters
	const digitsOnly = phone.replace(/\D/g, "");

	// Format based on length
	if (digitsOnly.length === 10) {
		// US format: (*************
		return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`;
	} else if (digitsOnly.length === 11 && digitsOnly.startsWith("1")) {
		// US format with country code: +1 (*************
		return `+1 (${digitsOnly.slice(1, 4)}) ${digitsOnly.slice(4, 7)}-${digitsOnly.slice(7)}`;
	}

	// Return original if can't format
	return phone;
}

/**
 * Validates and formats date to ISO string
 * 
 * @param date - Date string, Date object, or timestamp to validate and format
 * @returns ISO date string or null if invalid
 * 
 * @example
 * ```typescript
 * formatDateToISO("2024-01-01");           // "2024-01-01T00:00:00.000Z"
 * formatDateToISO(new Date());             // "2024-01-01T12:00:00.000Z"
 * formatDateToISO("invalid");              // null
 * ```
 */
export function formatDateToISO(date: string | Date | number): string | null {
	if (!date) {
		return null;
	}

	try {
		const dateObj = new Date(date);
		
		// Check if date is valid
		if (isNaN(dateObj.getTime())) {
			return null;
		}

		return dateObj.toISOString();
	} catch {
		return null;
	}
}

/**
 * Validates if a string is not empty after trimming
 * 
 * @param value - String value to validate
 * @returns True if string is not empty after trimming
 */
export function isNonEmptyString(value: unknown): value is string {
	return typeof value === "string" && value.trim().length > 0;
}

/**
 * Validates if a value is a positive number
 * 
 * @param value - Value to validate
 * @returns True if value is a positive number
 */
export function isPositiveNumber(value: unknown): value is number {
	return typeof value === "number" && value > 0 && !isNaN(value);
}

/**
 * Validates if a value is a valid ID (string or positive number)
 * 
 * @param value - Value to validate as ID
 * @returns True if value is a valid ID
 */
export function isValidId(value: unknown): value is string | number {
	if (typeof value === "string") {
		return value.trim().length > 0;
	}
	if (typeof value === "number") {
		return value > 0 && !isNaN(value);
	}
	return false;
}

/**
 * Validates if an object has required properties
 * 
 * @param obj - Object to validate
 * @param requiredProps - Array of required property names
 * @returns True if object has all required properties
 */
export function hasRequiredProperties(
	obj: Record<string, unknown>,
	requiredProps: string[]
): boolean {
	if (!obj || typeof obj !== "object") {
		return false;
	}

	return requiredProps.every(prop => 
		prop in obj && 
		obj[prop] !== null && 
		obj[prop] !== undefined &&
		obj[prop] !== ""
	);
}

/**
 * Validates patient data has required fields for sync
 * 
 * @param patient - Patient data object
 * @returns True if patient has required fields (email or phone)
 */
export function isValidPatientData(patient: {
	email?: string;
	phoneMobile?: string;
	firstName?: string;
	lastName?: string;
}): boolean {
	// Must have first name and last name
	if (!isNonEmptyString(patient.firstName) || !isNonEmptyString(patient.lastName)) {
		return false;
	}

	// Must have either valid email or valid phone
	const hasValidEmail = patient.email && isValidEmail(patient.email);
	const hasValidPhone = patient.phoneMobile && isValidPhone(patient.phoneMobile);

	return hasValidEmail || hasValidPhone;
}

/**
 * Validates contact data has required fields for sync
 * 
 * @param contact - Contact data object
 * @returns True if contact has required fields
 */
export function isValidContactData(contact: {
	email?: string;
	phone?: string;
	firstName?: string;
	lastName?: string;
}): boolean {
	// Must have first name and last name
	if (!isNonEmptyString(contact.firstName) || !isNonEmptyString(contact.lastName)) {
		return false;
	}

	// Must have either valid email or valid phone
	const hasValidEmail = contact.email && isValidEmail(contact.email);
	const hasValidPhone = contact.phone && isValidPhone(contact.phone);

	return hasValidEmail || hasValidPhone;
}
