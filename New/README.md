# DermaCare Bi-Directional Sync Service

A high-performance, stateless webhook service built for Cloudflare Workers that provides real-time bi-directional data synchronization between CliniCore (CC) and AutoPatient (AP) systems.

## 🚀 Overview

The DermaCare Sync Service enables seamless data flow between two healthcare management platforms:
- **CliniCore (CC)**: Practice management system for patient records, appointments, and billing
- **AutoPatient (AP)**: Patient engagement and marketing automation platform

### Key Features

- ⚡ **High Performance**: 25-second webhook completion guarantee
- 🔄 **Bi-Directional Sync**: Real-time data synchronization in both directions
- 🛡️ **Stateless Architecture**: Cloudflare Workers compatible with request-scoped caching
- 🔒 **Buffer Management**: Prevents duplicate processing with configurable time windows
- 📊 **Advanced Custom Fields**: Comprehensive patient analytics and LTV calculations
- 🚨 **Error Handling**: Robust retry mechanisms with exponential backoff
- 📈 **Performance Monitoring**: Real-time metrics and bottleneck identification

### Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CliniCore     │    │  DermaCare      │    │  AutoPatient    │
│   (CC)          │◄──►│  Sync Service   │◄──►│  (AP)           │
│                 │    │                 │    │                 │
│ • Patients      │    │ • Webhooks      │    │ • Contacts      │
│ • Appointments  │    │ • Data Transform│    │ • Appointments  │
│ • Invoices      │    │ • Buffer Mgmt   │    │ • Custom Fields │
│ • Payments      │    │ • Error Handling│    │ • Tags          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Development Workflow](#development-workflow)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)
- [Performance Optimization](#performance-optimization)
- [Contributing](#contributing)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm package manager
- Cloudflare Workers account
- Database access (PostgreSQL via Neon)
- ngrok for local webhook testing

### Local Development Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd DermaCare/Try/New
   pnpm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration values
   ```

3. **Start Development Server**
   ```bash
   pnpm dev
   ```

4. **Start ngrok Tunnel** (for webhook testing)
   ```bash
   pnpm tunnel
   ```

Your service will be available at:
- Local: `http://localhost:8787`
- ngrok: `https://proven-moose-hopefully.ngrok-free.app`

## 📦 Installation

### Development Environment

```bash
# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env

# Initialize database (if needed)
pnpm db:push

# Run development server
pnpm dev
```

### Production Deployment

```bash
# Build for production
pnpm build

# Deploy to Cloudflare Workers
pnpm deploy

# Deploy with environment
pnpm deploy:staging  # or pnpm deploy:production
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# Database Configuration
DATABASE_URL="postgresql://user:password@host:port/database"

# CliniCore API Configuration
CC_API_DOMAIN="https://ccdemo.clinicore.eu/api/v1"
CC_API_KEY="your-clinicore-api-key"

# AutoPatient API Configuration
AP_API_DOMAIN="https://services.leadconnectorhq.com"
AP_API_KEY="your-autopatient-api-key"
AP_LOCATION_ID="your-location-id"
AP_CALENDAR_ID="your-calendar-id"

# Performance Configuration (Optional)
WEBHOOK_TIMEOUT_MS=25000
DATABASE_QUERY_TIMEOUT_MS=1500
API_CALL_TIMEOUT_MS=8000
CACHE_TTL_SECONDS=300
SYNC_BUFFER_SECONDS=60
MAX_RETRIES=3
```

### Configuration Management

All configuration is centralized in `src/utils/configs.ts`. The system supports:

- **Environment-based configuration**: Different settings for development, staging, and production
- **Performance optimization**: Timeouts and thresholds optimized for 25-second webhook completion
- **Cache management**: Configurable TTL values for different data types
- **Retry mechanisms**: Exponential backoff with configurable limits
- **Error handling**: Comprehensive error logging and monitoring settings

### Key Configuration Sections

#### Performance Thresholds
```typescript
performance: {
  totalRequestTimeout: 25000,     // 25 seconds - Cloudflare Workers limit
  databaseQueryTimeout: 1500,     // 1.5 seconds per query
  apiCallTimeout: 8000,           // 8 seconds per API call
  memoryLimit: 128 * 1024 * 1024, // 128MB memory limit
}
```

#### Cache TTL Settings
```typescript
cacheTTLs: {
  patientData: 10 * 60 * 1000,    // 10 minutes
  appointmentData: 5 * 60 * 1000,  // 5 minutes
  customFields: 30 * 60 * 1000,    // 30 minutes
  apiResponses: 2 * 60 * 1000,     // 2 minutes
}
```

#### Retry Configuration
```typescript
retry: {
  maxAttempts: 3,        // Maximum retry attempts
  baseDelay: 1000,       // 1 second base delay
  maxDelay: 5000,        // 5 seconds maximum delay
  timeoutMs: 20000,      // 20 seconds total timeout
}
```

## 📡 API Documentation

### Webhook Endpoints

#### CliniCore Webhooks

**POST `/webhook`**

Receives webhooks from CliniCore for patient, appointment, invoice, and payment events.

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "event": "EntityWasCreated" | "EntityWasUpdated" | "EntityWasDeleted",
  "model": "Patient" | "Appointment" | "Invoice" | "Payment",
  "id": 12345,
  "payload": {
    // Entity-specific data
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "processingTime": 1234
}
```

**Supported Events:**
- `EntityWasCreated`: New entity created in CliniCore
- `EntityWasUpdated`: Existing entity updated in CliniCore
- `EntityWasDeleted`: Entity deleted in CliniCore

**Supported Models:**
- `Patient`: Patient records with demographics and contact info
- `Appointment`: Appointment scheduling and updates
- `Invoice`: Billing and invoice information
- `Payment`: Payment processing and status updates

#### AutoPatient Webhooks

**POST `/ap/contact`**

Receives contact-related webhooks from AutoPatient.

**Request Body:**
```json
{
  "event": "contact.created" | "contact.updated" | "contact.deleted",
  "data": {
    "contact": {
      "id": "ap_contact_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+**********",
      "customFields": []
    }
  }
}
```

**POST `/ap/appointment`**

Receives appointment-related webhooks from AutoPatient.

**Request Body:**
```json
{
  "event": "appointment.created" | "appointment.updated" | "appointment.deleted",
  "data": {
    "calendar": {
      "appointmentId": "ap_appointment_456",
      "startTime": "2024-01-01T10:00:00Z",
      "endTime": "2024-01-01T11:00:00Z",
      "title": "Consultation"
    },
    "contact_id": "ap_contact_123"
  }
}
```

#### Health Check

**GET `/health`**

Returns service health status and performance metrics.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "performance": {
    "averageResponseTime": 150,
    "activeConnections": 5,
    "memoryUsage": "45MB"
  }
}
```

### Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request payload",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Common Error Codes:**
- `VALIDATION_ERROR`: Invalid request data
- `AUTHENTICATION_ERROR`: Invalid API credentials
- `RATE_LIMIT_ERROR`: Too many requests
- `TIMEOUT_ERROR`: Request exceeded time limit
- `INTERNAL_ERROR`: Server-side error
- `EXTERNAL_API_ERROR`: Third-party API failure

### Rate Limiting

The service implements intelligent rate limiting:
- **CliniCore API**: 5 concurrent requests maximum
- **AutoPatient API**: 3 concurrent requests maximum
- **Database**: 1.5 second timeout per query
- **Overall**: 25 second total processing time

## 🔧 Development Workflow

### Project Structure

```
src/
├── api/                    # API clients and external integrations
│   ├── ccClient.ts        # CliniCore API client
│   ├── apClient.ts        # AutoPatient API client
│   └── index.ts           # API exports
├── database/              # Database configuration and schema
│   ├── schema.ts          # Drizzle ORM schema definitions
│   ├── queries.ts         # Optimized database queries
│   └── index.ts           # Database exports
├── helpers/               # Business logic and data transformation
│   ├── dataTransform.ts   # Data transformation utilities
│   ├── customFields.ts    # Custom field processing
│   └── index.ts           # Helper exports
├── processors/            # Webhook processing logic
│   ├── ccToApProcessor.ts # CC → AP data flow
│   ├── apToCcProcessor.ts # AP → CC data flow
│   └── index.ts           # Processor exports
├── types/                 # TypeScript type definitions
│   ├── cc.ts             # CliniCore types
│   ├── ap.ts             # AutoPatient types
│   └── index.ts          # Type exports
├── utils/                 # Utility functions and configuration
│   ├── configs.ts         # Centralized configuration
│   ├── validation.ts      # Data validation utilities
│   ├── errorLogger.ts     # Error logging and monitoring
│   ├── bufferManager.ts   # Duplicate prevention
│   └── index.ts           # Utility exports
└── index.ts              # Main application entry point
```

### Development Commands

```bash
# Development
pnpm dev                   # Start development server
pnpm dev:tunnel           # Start dev server + ngrok tunnel
pnpm tunnel               # Start ngrok tunnel only

# Building
pnpm build                # Build for production
pnpm type-check           # TypeScript type checking
pnpm lint                 # ESLint code linting
pnpm format               # Prettier code formatting

# Database
pnpm db:generate          # Generate database migrations
pnpm db:push              # Push schema changes to database
pnpm db:studio            # Open Drizzle Studio (database GUI)

# Testing
pnpm test                 # Run all tests
pnpm test:unit            # Run unit tests only
pnpm test:integration     # Run integration tests only
pnpm test:webhooks        # Test webhook endpoints

# Deployment
pnpm deploy               # Deploy to production
pnpm deploy:staging       # Deploy to staging environment
```

### Code Quality Standards

- **TypeScript**: Strict typing with no `any` types allowed
- **ESLint**: Enforced code style and best practices
- **Prettier**: Consistent code formatting
- **JSDoc**: Comprehensive documentation for all functions
- **Path Aliases**: Clean imports using `@api`, `@database`, `@helpers`, etc.

### Git Workflow

1. **Feature Development**
   ```bash
   git checkout -b feature/your-feature-name
   # Make changes
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/your-feature-name
   ```

2. **Code Review Process**
   - Create pull request with detailed description
   - Ensure all tests pass
   - Code review by team member
   - Merge after approval

3. **Release Process**
   - Tag releases with semantic versioning
   - Deploy to staging first
   - Production deployment after validation

## 🧪 Testing

### Webhook Testing Tool

The project includes a comprehensive webhook testing utility that creates real database records for testing:

```bash
# Test all webhook endpoints
node webhook-tester.js local all

# Test specific webhook types
node webhook-tester.js local cc-webhooks
node webhook-tester.js local ap-webhooks

# Test with cleanup
node webhook-tester.js local all --cleanup

# Test with verbose output
node webhook-tester.js local all --verbose
```

### Test Environments

- **Local**: `http://localhost:8787`
- **ngrok**: `https://proven-moose-hopefully.ngrok-free.app`
- **Staging**: `https://staging.dermacare-sync.workers.dev`
- **Production**: `https://dermacare-sync.workers.dev`

### Test Data Creation

The webhook tester automatically creates real database records:

```javascript
// Creates actual patient record
const testPatient = await createTestPatient(db, {
  firstName: "John",
  lastName: "Doe",
  email: "<EMAIL>",
  phone: "+**********"
});

// Uses real CC ID in webhook payload
const webhookPayload = {
  event: "EntityWasCreated",
  model: "Patient",
  id: testPatient.cc_id,  // Real database ID
  payload: JSON.parse(testPatient.cc_data)
};
```

### Unit Testing

```bash
# Run all unit tests
pnpm test:unit

# Run specific test file
pnpm test src/helpers/dataTransform.test.ts

# Run tests with coverage
pnpm test:coverage
```

### Integration Testing

```bash
# Test database operations
pnpm test:db

# Test API integrations
pnpm test:api

# Test end-to-end workflows
pnpm test:e2e
```

## 🚀 Deployment

### Staging Deployment

```bash
# Deploy to staging
pnpm deploy:staging

# Verify deployment
curl https://staging.dermacare-sync.workers.dev/health
```

### Production Deployment

```bash
# Build and deploy to production
pnpm build
pnpm deploy:production

# Verify deployment
curl https://dermacare-sync.workers.dev/health
```

### Environment-Specific Configuration

Each environment has its own configuration:

```bash
# Staging environment variables
wrangler secret put CC_API_KEY --env staging
wrangler secret put AP_API_KEY --env staging
wrangler secret put DATABASE_URL --env staging

# Production environment variables
wrangler secret put CC_API_KEY --env production
wrangler secret put AP_API_KEY --env production
wrangler secret put DATABASE_URL --env production
```

### Deployment Checklist

- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Performance benchmarks met
- [ ] Error monitoring configured
- [ ] Rollback plan prepared

## 🔍 Troubleshooting

### Common Issues

#### 1. Webhook Timeouts

**Symptoms:** Webhooks failing with timeout errors

**Solutions:**
```bash
# Check performance metrics
curl https://your-domain.workers.dev/health

# Review configuration
grep -r "timeout" src/utils/configs.ts

# Optimize database queries
pnpm db:analyze
```

#### 2. Database Connection Issues

**Symptoms:** Database connection errors or slow queries

**Solutions:**
```bash
# Test database connection
pnpm db:test-connection

# Check connection pool settings
grep -r "pool" src/database/

# Monitor query performance
pnpm db:monitor
```

#### 3. API Rate Limiting

**Symptoms:** External API calls failing with rate limit errors

**Solutions:**
```bash
# Check API client configuration
grep -r "concurrent" src/utils/configs.ts

# Review retry settings
grep -r "retry" src/utils/configs.ts

# Monitor API usage
curl https://your-domain.workers.dev/api/stats
```

#### 4. Memory Issues

**Symptoms:** Workers exceeding memory limits

**Solutions:**
```bash
# Check memory configuration
grep -r "memory" src/utils/configs.ts

# Review cache settings
grep -r "cache" src/utils/configs.ts

# Optimize data processing
pnpm analyze:memory
```

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
# Set debug environment variable
export DEBUG=dermacare:*

# Run with verbose logging
pnpm dev --verbose

# Check specific modules
export DEBUG=dermacare:database,dermacare:api
```

### Log Analysis

```bash
# View recent logs
wrangler tail --env production

# Filter specific errors
wrangler tail --env production | grep ERROR

# Monitor performance
wrangler tail --env production | grep PERFORMANCE
```

## ⚡ Performance Optimization

### Performance Targets

The service is optimized to meet strict performance requirements:

| Operation | Target | Monitoring |
|-----------|--------|------------|
| Total webhook processing | < 25 seconds | Real-time alerts |
| Database queries | < 1.5 seconds | Query performance logs |
| API calls | < 8 seconds | Circuit breaker protection |
| Memory usage | < 128MB | Memory monitoring |
| Cache operations | < 100ms | Cache hit rate tracking |

### Optimization Techniques

#### 1. Request-Scoped Caching

```typescript
// Efficient caching with TTL management
const cache = new Map<string, CacheEntry>();

// Cache patient data for request duration
const cachedPatient = cache.get(`patient:${id}`);
if (cachedPatient && cachedPatient.expires > Date.now()) {
  return cachedPatient.data;
}
```

#### 2. Parallel Processing

```typescript
// Process multiple operations concurrently
const [patient, appointments, customFields] = await Promise.all([
  fetchPatientData(id),
  fetchAppointments(id),
  calculateCustomFields(id)
]);
```

#### 3. Database Query Optimization

```typescript
// Optimized queries with proper indexing
const patients = await db
  .select()
  .from(patient)
  .where(inArray(patient.ccId, ccIds))  // Uses index
  .limit(100);                          // Prevents large result sets
```

#### 4. Circuit Breaker Pattern

```typescript
// Protect against cascading failures
if (circuitBreaker.isOpen()) {
  throw new Error('Service temporarily unavailable');
}

try {
  const result = await externalApiCall();
  circuitBreaker.recordSuccess();
  return result;
} catch (error) {
  circuitBreaker.recordFailure();
  throw error;
}
```

### Performance Monitoring

#### Real-Time Metrics

```bash
# View performance dashboard
curl https://your-domain.workers.dev/metrics

# Check specific operation performance
curl https://your-domain.workers.dev/metrics/database

# Monitor API call performance
curl https://your-domain.workers.dev/metrics/api
```

#### Performance Alerts

The system automatically alerts on:
- Requests exceeding 20 seconds (80% of limit)
- Database queries over 1 second
- API calls over 6 seconds
- Memory usage over 100MB
- Error rates above 5%

### Optimization Best Practices

1. **Use Request-Scoped Caching**: Cache data for the duration of the request
2. **Implement Circuit Breakers**: Protect against external service failures
3. **Optimize Database Queries**: Use proper indexing and limit result sets
4. **Parallel Processing**: Execute independent operations concurrently
5. **Monitor Performance**: Track metrics and set up alerts
6. **Graceful Degradation**: Handle failures without breaking the entire flow

## 🤝 Contributing

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/dermacare-sync.git
   cd dermacare-sync/Try/New
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   ```

3. **Set Up Environment**
   ```bash
   cp .env.example .env
   # Configure your environment variables
   ```

4. **Run Tests**
   ```bash
   pnpm test
   ```

### Contribution Guidelines

#### Code Standards

- **TypeScript**: Use strict typing, no `any` types
- **Documentation**: Add JSDoc comments for all functions
- **Testing**: Write tests for new features and bug fixes
- **Performance**: Ensure changes don't exceed performance thresholds
- **Error Handling**: Implement comprehensive error handling

#### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow existing code patterns
   - Add comprehensive tests
   - Update documentation
   - Ensure TypeScript compilation

3. **Test Changes**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

4. **Submit Pull Request**
   - Provide detailed description
   - Include test results
   - Reference related issues

#### Code Review Checklist

- [ ] Code follows TypeScript best practices
- [ ] All functions have JSDoc documentation
- [ ] Tests cover new functionality
- [ ] Performance requirements met
- [ ] Error handling implemented
- [ ] Configuration centralized
- [ ] No hardcoded values

### Architecture Decisions

When contributing, consider these architectural principles:

1. **Stateless Design**: All code must be compatible with Cloudflare Workers
2. **Performance First**: 25-second webhook completion is non-negotiable
3. **Error Resilience**: Implement retry mechanisms and graceful degradation
4. **Configuration Management**: Use centralized configuration
5. **Type Safety**: Maintain strict TypeScript typing
6. **Documentation**: Keep documentation up-to-date

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:

- **Documentation**: This README and inline code comments
- **Issues**: GitHub Issues for bug reports and feature requests
- **Performance**: Monitor dashboard at `/health` endpoint
- **Logs**: Use `wrangler tail` for real-time log monitoring

---

**Built with ❤️ for seamless healthcare data synchronization**
